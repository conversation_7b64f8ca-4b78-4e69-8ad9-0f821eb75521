package com.nttdata.ndvn.order.web.controller;

import com.nttdata.ndvn.order.application.dto.CreateShipmentRequest;
import com.nttdata.ndvn.order.application.dto.ShipmentResponse;
import com.nttdata.ndvn.order.application.service.ShipmentApplicationService;
import com.nttdata.ndvn.order.domain.model.ShipmentStatus;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * REST controller for shipment management operations.
 * 
 * This controller provides endpoints for shipment creation, tracking,
 * status updates, and delivery management.
 */
@RestController
@RequestMapping("/api/v1/shipments")
@Tag(name = "Shipment Management", description = "Shipment tracking and delivery management operations")
public class ShipmentController {
    
    private static final Logger logger = LoggerFactory.getLogger(ShipmentController.class);
    
    private final ShipmentApplicationService shipmentApplicationService;
    
    @Autowired
    public ShipmentController(ShipmentApplicationService shipmentApplicationService) {
        this.shipmentApplicationService = shipmentApplicationService;
    }
    
    /**
     * Creates a new shipment for an order.
     * 
     * @param request the shipment creation request
     * @return the created shipment response
     */
    @PostMapping
    @Operation(summary = "Create shipment", description = "Creates a new shipment for an order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Shipment created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid shipment data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('FULFILLMENT_MANAGER')")
    public ResponseEntity<ShipmentResponse> createShipment(@Valid @RequestBody CreateShipmentRequest request) {
        logger.info("Creating shipment for order: {}", request.getOrderId());
        
        try {
            ShipmentResponse response = shipmentApplicationService.createShipment(request);
            logger.info("Shipment created successfully for order: {}", request.getOrderId());
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (ShipmentApplicationService.ShipmentValidationException e) {
            logger.warn("Shipment validation failed: {}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        } catch (ShipmentApplicationService.OrderNotFoundException e) {
            logger.warn("Order not found for shipment: {}", request.getOrderId());
            throw new NotFoundException(e.getMessage());
        }
    }
    
    /**
     * Retrieves shipments for an order.
     * 
     * @param orderId the order ID
     * @return list of shipment responses
     */
    @GetMapping("/order/{orderId}")
    @Operation(summary = "Get order shipments", description = "Retrieves all shipments for a specific order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Shipments retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<List<ShipmentResponse>> getOrderShipments(
            @Parameter(description = "Order ID", required = true) @PathVariable UUID orderId) {
        logger.debug("Retrieving shipments for order: {}", orderId);
        
        List<ShipmentResponse> shipments = shipmentApplicationService.getOrderShipments(orderId);
        return ResponseEntity.ok(shipments);
    }
    
    /**
     * Tracks a shipment by tracking number.
     * 
     * @param trackingNumber the tracking number
     * @return the shipment tracking response
     */
    @GetMapping("/track/{trackingNumber}")
    @Operation(summary = "Track shipment", description = "Tracks a shipment by its tracking number")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Shipment tracking information retrieved"),
        @ApiResponse(responseCode = "404", description = "Shipment not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ShipmentTrackingResponse> trackShipment(
            @Parameter(description = "Tracking number", required = true) @PathVariable String trackingNumber) {
        logger.debug("Tracking shipment: {}", trackingNumber);
        
        try {
            ShipmentTrackingResponse response = shipmentApplicationService.trackShipment(trackingNumber);
            return ResponseEntity.ok(response);
        } catch (ShipmentApplicationService.ShipmentNotFoundException e) {
            logger.warn("Shipment not found for tracking: {}", trackingNumber);
            throw new NotFoundException(e.getMessage());
        }
    }
    
    /**
     * Updates shipment status.
     * 
     * @param shipmentId the shipment ID
     * @param request the status update request
     * @return the updated shipment response
     */
    @PutMapping("/{shipmentId}/status")
    @Operation(summary = "Update shipment status", description = "Updates the status of a shipment")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Shipment status updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid status transition"),
        @ApiResponse(responseCode = "404", description = "Shipment not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('FULFILLMENT_MANAGER')")
    public ResponseEntity<ShipmentResponse> updateShipmentStatus(
            @Parameter(description = "Shipment ID", required = true) @PathVariable UUID shipmentId,
            @Valid @RequestBody UpdateShipmentStatusRequest request) {
        logger.info("Updating shipment status: {} to {}", shipmentId, request.getStatus());
        
        try {
            ShipmentResponse response = shipmentApplicationService.updateShipmentStatus(
                shipmentId, request.getStatus(), request.getNotes());
            logger.info("Shipment status updated successfully: {} to {}", shipmentId, request.getStatus());
            return ResponseEntity.ok(response);
        } catch (ShipmentApplicationService.ShipmentNotFoundException e) {
            logger.warn("Shipment not found for status update: {}", shipmentId);
            throw new NotFoundException(e.getMessage());
        } catch (ShipmentApplicationService.ShipmentValidationException e) {
            logger.warn("Shipment status update validation failed: {}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        }
    }
    
    /**
     * Marks a shipment as delivered.
     * 
     * @param shipmentId the shipment ID
     * @param request the delivery confirmation request
     * @return the updated shipment response
     */
    @PostMapping("/{shipmentId}/deliver")
    @Operation(summary = "Mark shipment as delivered", description = "Marks a shipment as delivered")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Shipment marked as delivered successfully"),
        @ApiResponse(responseCode = "400", description = "Shipment cannot be marked as delivered"),
        @ApiResponse(responseCode = "404", description = "Shipment not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('DELIVERY_AGENT')")
    public ResponseEntity<ShipmentResponse> markAsDelivered(
            @Parameter(description = "Shipment ID", required = true) @PathVariable UUID shipmentId,
            @RequestBody(required = false) DeliveryConfirmationRequest request) {
        logger.info("Marking shipment as delivered: {}", shipmentId);
        
        String deliveryNotes = request != null ? request.getDeliveryNotes() : null;
        
        try {
            ShipmentResponse response = shipmentApplicationService.markAsDelivered(shipmentId, deliveryNotes);
            logger.info("Shipment marked as delivered successfully: {}", shipmentId);
            return ResponseEntity.ok(response);
        } catch (ShipmentApplicationService.ShipmentNotFoundException e) {
            logger.warn("Shipment not found for delivery: {}", shipmentId);
            throw new NotFoundException(e.getMessage());
        } catch (ShipmentApplicationService.ShipmentValidationException e) {
            logger.warn("Shipment delivery validation failed: {}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        }
    }
    
    /**
     * Retrieves shipments by status.
     * 
     * @param status the shipment status
     * @param pageable pagination parameters
     * @return page of shipment responses
     */
    @GetMapping("/status/{status}")
    @Operation(summary = "Get shipments by status", description = "Retrieves shipments filtered by status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Shipments retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('FULFILLMENT_MANAGER')")
    public ResponseEntity<Page<ShipmentResponse>> getShipmentsByStatus(
            @Parameter(description = "Shipment status", required = true) @PathVariable ShipmentStatus status,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("Retrieving shipments by status: {}", status);
        
        Page<ShipmentResponse> shipments = shipmentApplicationService.getShipmentsByStatus(status, pageable);
        return ResponseEntity.ok(shipments);
    }
    
    /**
     * DTO for shipment tracking response.
     */
    public static class ShipmentTrackingResponse {
        private UUID shipmentId;
        private String trackingNumber;
        private ShipmentStatus status;
        private String carrier;
        private List<TrackingEvent> trackingEvents;
        
        // Getters and setters
        public UUID getShipmentId() {
            return shipmentId;
        }
        
        public void setShipmentId(UUID shipmentId) {
            this.shipmentId = shipmentId;
        }
        
        public String getTrackingNumber() {
            return trackingNumber;
        }
        
        public void setTrackingNumber(String trackingNumber) {
            this.trackingNumber = trackingNumber;
        }
        
        public ShipmentStatus getStatus() {
            return status;
        }
        
        public void setStatus(ShipmentStatus status) {
            this.status = status;
        }
        
        public String getCarrier() {
            return carrier;
        }
        
        public void setCarrier(String carrier) {
            this.carrier = carrier;
        }
        
        public List<TrackingEvent> getTrackingEvents() {
            return trackingEvents;
        }
        
        public void setTrackingEvents(List<TrackingEvent> trackingEvents) {
            this.trackingEvents = trackingEvents;
        }
        
        public static class TrackingEvent {
            private ShipmentStatus status;
            private String description;
            private String location;
            private java.time.LocalDateTime timestamp;
            
            // Getters and setters
            public ShipmentStatus getStatus() {
                return status;
            }
            
            public void setStatus(ShipmentStatus status) {
                this.status = status;
            }
            
            public String getDescription() {
                return description;
            }
            
            public void setDescription(String description) {
                this.description = description;
            }
            
            public String getLocation() {
                return location;
            }
            
            public void setLocation(String location) {
                this.location = location;
            }
            
            public java.time.LocalDateTime getTimestamp() {
                return timestamp;
            }
            
            public void setTimestamp(java.time.LocalDateTime timestamp) {
                this.timestamp = timestamp;
            }
        }
    }
    
    /**
     * DTO for update shipment status request.
     */
    public static class UpdateShipmentStatusRequest {
        @NotNull(message = "Status is required")
        private ShipmentStatus status;
        
        @Size(max = 500, message = "Notes must not exceed 500 characters")
        private String notes;
        
        public ShipmentStatus getStatus() {
            return status;
        }
        
        public void setStatus(ShipmentStatus status) {
            this.status = status;
        }
        
        public String getNotes() {
            return notes;
        }
        
        public void setNotes(String notes) {
            this.notes = notes;
        }
    }
    
    /**
     * DTO for delivery confirmation request.
     */
    public static class DeliveryConfirmationRequest {
        @Size(max = 500, message = "Delivery notes must not exceed 500 characters")
        private String deliveryNotes;
        
        public String getDeliveryNotes() {
            return deliveryNotes;
        }
        
        public void setDeliveryNotes(String deliveryNotes) {
            this.deliveryNotes = deliveryNotes;
        }
    }
    
    /**
     * Custom exception for bad request errors.
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public static class BadRequestException extends RuntimeException {
        public BadRequestException(String message) {
            super(message);
        }
    }
    
    /**
     * Custom exception for not found errors.
     */
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public static class NotFoundException extends RuntimeException {
        public NotFoundException(String message) {
            super(message);
        }
    }
}
