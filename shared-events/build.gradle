plugins {
    id 'java-library'
    id 'maven-publish'
    id 'org.springframework.boot' version '3.4.1' apply false
    id 'io.spring.dependency-management' version '1.1.7' apply false
}

apply plugin: 'io.spring.dependency-management'

group = 'com.nttdata.ndvn'
version = '1.0.0-SNAPSHOT'

description = 'Shared Events Framework - Common event publishing and consumption infrastructure'

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
    maven { url 'https://packages.confluent.io/maven/' }
}

dependencyManagement {
    imports {
        mavenBom 'org.springframework.boot:spring-boot-dependencies:3.4.1'
        mavenBom 'org.springframework.cloud:spring-cloud-dependencies:2024.0.0'
        mavenBom 'org.terasoluna.gfw:terasoluna-gfw-parent:5.10.0.RELEASE'
    }
}

dependencies {
    // Spring Boot
    api 'org.springframework.boot:spring-boot-starter'
    api 'org.springframework.boot:spring-boot-starter-validation'
    api 'org.springframework.boot:spring-boot-starter-actuator'

    // Kafka
    api 'org.springframework.kafka:spring-kafka'
    api 'org.apache.kafka:kafka-clients:3.8.1'
    api 'io.confluent:kafka-avro-serializer:7.7.1'
    
    // JSON processing
    api 'com.fasterxml.jackson.core:jackson-databind'
    api 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // Metrics
    api 'io.micrometer:micrometer-core'
    api 'io.micrometer:micrometer-registry-prometheus'
    
    // Validation
    api 'jakarta.validation:jakarta.validation-api'
    
    // Logging
    api 'org.slf4j:slf4j-api'
    
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.kafka:spring-kafka-test'
    testImplementation 'org.testcontainers:kafka'
}

test {
    useJUnitPlatform()
}

publishing {
    publications {
        maven(MavenPublication) {
            from components.java

            artifactId = 'shared-events'

            pom {
                name = 'NDVN Shared Events'
                description = 'Shared Events Framework - Common event publishing and consumption infrastructure'
            }
        }
    }
}
